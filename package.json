{"name": "productivity-hub", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@google/generative-ai": "^0.24.0", "@react-oauth/google": "^0.12.1", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-virtual": "^3.13.2", "@types/react-virtualized": "^9.22.2", "date-fns": "^3.3.1", "dotenv": "^16.4.7", "emoji-picker-react": "^4.12.0", "framer-motion": "^12.5.0", "lucide-react": "^0.344.0", "openai": "^5.1.1", "opus-recorder": "^8.0.5", "react": "^18.3.1", "react-audio-visualize": "^1.2.0", "react-avatar-editor": "^13.0.2", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-intersection-observer": "^9.16.0", "react-is": "^19.0.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.22.2", "react-spotify-embed": "^2.0.3", "react-syntax-highlighter": "^5.8.0", "react-virtualized": "^9.22.6", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "reactflow": "^11.10.4", "supabase": "^2.19.7", "uuid": "^11.1.0", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/dotenv": "^8.2.3", "@types/node": "^22.10.5", "@types/react": "^18.3.5", "@types/react-avatar-editor": "^13.0.4", "@types/react-dom": "^18.3.0", "@types/react-helmet-async": "^1.0.1", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.2.4"}}