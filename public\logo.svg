<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="url(#gradientBlue)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-network">
  <defs>
    <linearGradient id="gradientBlue" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#41D1FF" />
      <stop offset="100%" stop-color="#1E90FF" />
    </linearGradient>
  </defs>
  <circle cx="12" cy="12" r="4"></circle>
  <path d="M16 8c2.828 0 4-1.172 4-4"></path>
  <path d="M8 8C5.172 8 4 6.828 4 4"></path>
  <path d="M8 16c-2.828 0-4 1.172-4 4"></path>
  <path d="M16 16c2.828 0 4 1.172 4 4"></path>
</svg>