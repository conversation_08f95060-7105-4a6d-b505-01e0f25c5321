const defaultNodeStyles = {
  input: {
    background: "#1f2937",
    color: "#fff",
    border: "4px solid transparent",
    borderRadius: "8px",
    padding: "10px",
    width: "160px",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
    backgroundClip: "border-box",
  },
  default: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "120px",
    minWidth: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  link: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  "youtube-video": {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "320px",
    minWidth: "320px",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  mindmap: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  spotify: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "200px",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  soundcloud: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "200px",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },

  instagram: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  twitter: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  tiktok: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  youtube: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  facebook: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "10px",
    width: "auto",
    minWidth: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  image: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "0",
    minWidth: "auto",
    width: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  audio: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "0",
    minWidth: "250px",
    width: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
  playlist: {
    background: "#1f2937",
    color: "#fff",
    border: "2px solid #374151",
    borderRadius: "16px",
    padding: "0",
    minWidth: "250px",
    width: "auto",
    transition: "border-color 0.2s ease-in-out",
    wordWrap: "break-word",
    whiteSpace: "pre-wrap",
    overflow: "visible",
    textShadow: "0 1px 2px rgba(0, 0, 0, 1)",
  },
};

export default defaultNodeStyles;
