"use client"

import { useState, useEffect } from "react"
import { useLocation } from "react-router-dom"
import { usePageTitle } from "../hooks/usePageTitle"
import { LoginForm } from "../components/LoginForm"
import { ImageCarousel } from "../components/ImageCarousel"

const images = [
  "/src/assets/picar/map1.jpg",
  "/src/assets/picar/map2.jpg",
  "/src/assets/picar/map3.jpg",
  "/src/assets/picar/map4.jpg",
  "/src/assets/picar/map5.jpg",
]

export default function Home() {
  const [showCarousel, setShowCarousel] = useState(true)
  const location = useLocation()
  const successMessage = location.state?.successMessage || "" // Get success message from state

  usePageTitle("Welcome")

  useEffect(() => {
    const handleResize = () => {
      setShowCarousel(window.innerWidth >= 1024)
    }

    window.addEventListener("resize", handleResize)
    handleResize()

    return () => window.removeEventListener("resize", handleResize)
  }, [])

  return (
    <div className="flex h-[100dvh] overflow-hidden bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
      {/* Enhanced Left Panel */}
      <div className="w-full lg:w-1/2 flex items-center justify-center relative">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/20 to-slate-800/20 backdrop-blur-3xl"></div>

        <div className="w-full max-w-md px-6 lg:px-8 relative z-10">
          {/* Enhanced Brand Title */}
          <div className="text-center mb-12">
            <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent drop-shadow-2xl">
              MindMeet
            </h1>
            <p className="text-slate-400 text-lg font-medium">Connect minds, create together</p>
          </div>

          {/* Enhanced Login Form Container */}
          <div className="bg-gradient-to-br from-slate-800/40 to-slate-900/40 backdrop-blur-xl rounded-2xl p-8 border border-slate-700/30 shadow-2xl">
            <LoginForm successMessage={successMessage} />
          </div>
        </div>
      </div>

      {/* Enhanced Right Panel */}
      {showCarousel && (
        <div className="hidden lg:flex lg:w-1/2 items-center justify-center px-8 relative">
          {/* Background gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-l from-slate-900/60 to-transparent"></div>

          <div className="w-[360px] relative z-10">
            {/* Enhanced carousel container */}
            <div className="bg-gradient-to-br from-slate-800/20 to-slate-900/20 backdrop-blur-sm rounded-2xl p-4 border border-slate-700/20 shadow-2xl">
              <ImageCarousel images={images} />
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-full blur-xl"></div>
          </div>
        </div>
      )}
    </div>
  )
}
