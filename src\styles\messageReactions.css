/* Message reaction styles */
.message-container {
  position: relative;
  margin-bottom: 4px !important; /* Default space below messages */
}

/* Add more space below messages with reactions */
.message-container.has-reactions {
  margin-bottom: 16px !important; /* More space for messages with reactions */
}

/* Even more space for user messages with reactions */
.user-message.has-reactions {
  margin-bottom: 20px !important; /* Extra space for user messages with reactions */
}

/* Style for the reaction container */
.message-reactions {
  pointer-events: auto;
}

/* Ensure reactions are visible and clickable */
.message-reactions img {
  display: inline-block;
}

/* Make the reaction container more compact */
.message-reactions > div {
  min-width: 0;
  display: inline-flex;
  align-items: center;
}

/* Add a slight offset to make reactions appear below the message */
.user-message .message-reactions {
  transform: translateY(calc(100% - 4px)) translateX(-4px);
}

/* Non-user messages have reactions on the left */
.message-container:not(.user-message) .message-reactions {
  transform: translateY(calc(100% - 4px)) translateX(4px);
}

/* Ensure reaction menu images are properly displayed */
.reaction-menu-images {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reaction-menu-images img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* GIF styles */
.gif-menu {
  max-height: 400px;
  overflow-y: auto;
}

.gif-menu img {
  transition: transform 0.2s ease;
}

.gif-menu button:hover img {
  transform: scale(1.05);
}

/* GIF in messages */
.message-container img[alt="GIF"] {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* GIF label in reply preview */
.reply-gif-label {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: #93c5fd; /* blue-300 */
  font-size: 0.875rem;
}
