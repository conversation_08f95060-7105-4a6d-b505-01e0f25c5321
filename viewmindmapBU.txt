import type React from "react"
import { useEffect, useState, use<PERSON><PERSON>back, useRef } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom"
import ReactFlow, { Background, Controls, ReactFlowProvider, type ReactFlowInstance } from "reactflow"
import "reactflow/dist/style.css"
import { supabase } from "../supabaseClient"
import { useAuthStore } from "../store/authStore"
import { usePageTitle } from '../hooks/usePageTitle'
import { SpotifyViewNode } from "../components/SpotifyViewNode"
import { SoundCloudViewNode } from "../components/SoundCloudViewNode"
import { YouTubeViewNode } from "../components/YouTubeViewNode"
import { ImageNode } from "../components/ImageNode"
import { AudioNode } from "../components/AudioNode"
import { PlaylistNode } from "../components/PlaylistNode"
import { SocialMediaNode } from "../components/SocialMediaNode"
import { LinkNode } from "../components/LinkNode"
import { MindMapNode } from "../components/MindMapNode"
import { prepareNodesForRendering } from "../utils/reactFlowUtils"
import { ChevronDown, Maximize2, Heart, Share2, Edit2, Trash2, MoreHorizontal, X, Check, Loader, UserPlus, UserMinus } from "lucide-react"
import SimilarMindMapNode from "../components/SimilarMindMapNode"
import ShareModal from "../components/ShareModal"
import InfoModal from "../components/InfoModal"
import { useNotificationStore } from '../store/notificationStore';
import { useMindMapActions } from '../hooks/useMindMapActions';
import eventEmitter from '../services/eventService';
import defaultNodeStyles from "../config/defaultNodeStyles";

import type { NodeTypes, NodeProps } from "reactflow"

const nodeTypes: NodeTypes = {
  spotify: SpotifyViewNode,
  soundcloud: SoundCloudViewNode,
  "youtube-video": YouTubeViewNode,
  image: ImageNode,
  audio: AudioNode,
  playlist: PlaylistNode,
  instagram: SocialMediaNode as unknown as React.FC<NodeProps>,
  twitter: SocialMediaNode as unknown as React.FC<NodeProps>,
  facebook: SocialMediaNode as unknown as React.FC<NodeProps>,
  youtube: SocialMediaNode as unknown as React.FC<NodeProps>,
  tiktok: SocialMediaNode as unknown as React.FC<NodeProps>,
  link: LinkNode,
  mindmap: MindMapNode,
}

const ViewMindMap: React.FC = () => {
  const { username, id } = useParams<{ username: string; id: string }>()
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [userProfile, setUserProfile] = useState<{ avatar_url: string | null } | null>(null)
  const [currentMap, setCurrentMap] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set())
  const reactFlowWrapperRef = useRef<HTMLDivElement>(null)
  const [similarMindmaps, setSimilarMindmaps] = useState<any[]>([])
  const [creatorProfile, setCreatorProfile] = useState<{ avatar_url: string | null, id?: string, followed_by?: string[], followers?: number } | null>(null)
  const [isShareModalOpen, setIsShareModalOpen] = useState(false)
  const [comments, setComments] = useState<any[]>([])
  const [newComment, setNewComment] = useState("")
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null)
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [highlightedCommentId, setHighlightedCommentId] = useState<string | null>(null)
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null)
  const [similarMindmapsCollapsed, setSimilarMindmapsCollapsed] = useState(false)
  const isCreator = user?.username === username
    // Check if current user is a collaborator
  const isCollaborator = currentMap?.collaborators && user?.id ? 
    currentMap.collaborators.includes(user.id) : false

  // Add mindmap actions hook
  const { handleLike: hookHandleLike, handleSave: hookHandleSave } = useMindMapActions({
    onLikeUpdate: (mapId, newLikes, newLikedBy) => {
      setCurrentMap((prev: any) => 
        prev && prev.id === mapId ? { ...prev, likes: newLikes, liked_by: newLikedBy, likedBy: newLikedBy } : prev
      );
    },
    onSaveUpdate: (mapId, newSaves, newSavedBy) => {
      setCurrentMap((prev: any) => 
        prev && prev.id === mapId ? { ...prev, saves: newSaves, saved_by: newSavedBy, savedBy: newSavedBy } : prev
      );
    }
  });

  // Dynamic page title
  usePageTitle(currentMap && username ? `${currentMap.title || 'Untitled'} by @${username}` : 'Loading...');

  useEffect(() => {
    const fetchMindMap = async () => {
      setLoading(true)

      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("id")
        .eq("username", username)
        .single()

      if (profileError || !profile) {
        console.error("Error fetching profile:", profileError || "Profile not found")
        if (username) {
          navigate(`/${username}`)
        } else {
          navigate("/mindmap")
        }
        return
      }      const { data: map, error: mapError } = await supabase
        .from("mindmaps")
        .select("key, id, title, json_data, likes, liked_by, saves, saved_by, updated_at, visibility, description, is_main, collaborators, published_at")
        .eq("id", id)
        .eq("creator", profile.id)
        .single()

      if (mapError || !map) {
        console.error("Error fetching mind map:", mapError || "Mind map not found")
        if (mapError && mapError.code === 'PGRST116') {
          console.log(`Redirecting to user profile: /${username}`)
          navigate(`/${username}`)
        } else {
          navigate(`/${username}`)
        }
        return
      }      try {
        const processedNodes = prepareNodesForRendering(map.json_data?.nodes || []) || []

        const processedEdges = map.json_data?.edges.map((edge: any) => ({
          ...edge,
          id: edge.id || `e-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          source: edge.source,
          target: edge.target,
          type: edge.type || 'default'
        })) || []

        setCurrentMap({
          ...map,
          nodes: processedNodes,
          edges: processedEdges,
          likedBy: map.liked_by || [],
          saves: map.saves || 0,
          savedBy: map.saved_by || [],
          creator: profile.id,
        })

        setTimeout(() => {
          setLoading(false)
        }, 300)
      } catch (error) {
        console.error("Error processing mindmap data:", error)
        if (username) {
          navigate(`/${username}`)
        } else {
          navigate("/mindmap")
        }
      }
    }

    if (user) {
      fetchMindMap()
    } else {
      navigate("/login")
    }
  }, [username, id, user, navigate])

  useEffect(() => {
    const fetchSimilarMindmaps = async () => {
      const { data: mindmaps, error } = await supabase
        .from("mindmaps")
        .select("id, title, json_data, creator")
        .eq("visibility", "public")

      if (error) {
        console.error("Error fetching similar mindmaps:", error)
      } else {
        const shuffledMindmaps = mindmaps.sort(() => Math.random() - 0.5).slice(0, 5)
        setSimilarMindmaps(shuffledMindmaps || [])
      }
    }

    fetchSimilarMindmaps()
  }, [])

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (user) {
        const { data: profile, error } = await supabase.from("profiles").select("avatar_url").eq("id", user.id).single()

        if (error) {
          console.error("Error fetching user profile:", error)
        } else {
          setUserProfile(profile)
        }
      }
    }

    fetchUserProfile()
  }, [user])

  useEffect(() => {
    const fetchCreatorProfile = async () => {
      const { data: profile, error } = await supabase
        .from("profiles")
        .select("id, avatar_url, followed_by, followers")
        .eq("username", username)
        .single()

      if (error) {
        console.error("Error fetching creator profile:", error)
      } else {
        setCreatorProfile(profile)
        if (user?.id && profile.followed_by) {
          setIsFollowing(profile.followed_by.includes(user.id))
        }
      }
    }

    if (username) {
      fetchCreatorProfile()
    }
  }, [username, user?.id])

  useEffect(() => {
    const fetchComments = async () => {
      if (!currentMap?.key) return;

      try {
        const { data, error } = await supabase
          .from("comments")
          .select("id, content, created_at, edited_at, user_id, parent_id, likes, liked_by, profiles(username, avatar_url, id)")
          .eq("mindmap_id", currentMap.key)
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching comments:", error);
        } else {
          const topLevelComments: any[] = [];
          const replyMap: Record<string, any[]> = {};

          data.forEach((comment: any) => {
            if (!comment.parent_id) {
              topLevelComments.push({
                ...comment,
                replies: [],
                showReplies: false
              });
            } else {
              if (!replyMap[comment.parent_id]) {
                replyMap[comment.parent_id] = [];
              }
              replyMap[comment.parent_id].push(comment);
            }
          });

          topLevelComments.forEach((comment: any) => {
            if (replyMap[comment.id]) {
              comment.replies = replyMap[comment.id];
            }
          });

          setComments(topLevelComments);
        }
      } catch (error) {
        console.error("Unexpected error fetching comments:", error);
      }
    };

    fetchComments();
  }, [currentMap?.key]);

  useEffect(() => {
    if (loading || comments.length === 0) return;

    setTimeout(() => {
      const hash = window.location.hash;

      if (hash.startsWith('#comment-')) {
        const commentId = hash.replace('#comment-', '');
        setHighlightedCommentId(commentId);

        const commentElement = document.getElementById(`comment-${commentId}`);
        if (commentElement) {
          commentElement.scrollIntoView({ behavior: 'smooth' });

          setTimeout(() => {
            setHighlightedCommentId(null);
          }, 5000);

          const newUrl = window.location.pathname + window.location.search;
          window.history.replaceState({}, document.title, newUrl);
        } else {
          let commentFound = false;

          for (const comment of comments) {
            if (comment.replies && comment.replies.some((reply: any) => reply.id === commentId)) {
              setComments(prev =>
                prev.map(c => c.id === comment.id ? { ...c, showReplies: true } : c)
              );

              setTimeout(() => {
                const replyElement = document.getElementById(`comment-${commentId}`);
                if (replyElement) {
                  replyElement.scrollIntoView({ behavior: 'smooth' });
                  commentFound = true;
                }
              }, 100);

              break;
            }
          }

          if (!commentFound) {
            const commentsSection = document.getElementById('comments-section');
            if (commentsSection) {
              commentsSection.scrollIntoView({ behavior: 'smooth' });
            }
          }
        }
      }
      else if (hash === '#comments-section') {
        const commentsSection = document.getElementById('comments-section');
        if (commentsSection) {
          commentsSection.scrollIntoView({ behavior: 'smooth' });

          const newUrl = window.location.pathname + window.location.search;
          window.history.replaceState({}, document.title, newUrl);
        }
      }
    }, 500);
  }, [loading, comments]);

  const totalCommentsCount = comments.reduce(
    (count, comment) => count + 1 + (comment.replies?.length || 0),
    0
  )

  const onInit = useCallback((instance: ReactFlowInstance) => {
    setReactFlowInstance(instance)
    setTimeout(() => {
      if (instance) {
        instance.fitView({ padding: 0.2 })
      }
    }, 300)
  }, [])

  const handleResize = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({ padding: 0.2 })
    }
  }, [reactFlowInstance])

  useEffect(() => {
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [handleResize])

  useEffect(() => {
    if (!loading && reactFlowInstance && currentMap) {
      setTimeout(() => {
        reactFlowInstance.fitView({ padding: 0.2 })
      }, 300)
    }
  }, [loading, reactFlowInstance, currentMap])

  useEffect(() => {
    if (!loading && currentMap && currentMap.nodes && currentMap.nodes.length > 0) {
      const checkNodesVisibility = setTimeout(() => {
        if (reactFlowInstance) {
          const { x, y, zoom } = reactFlowInstance.getViewport()
          reactFlowInstance.setViewport({ x: x + 0.1, y, zoom })
          reactFlowInstance.fitView({ padding: 0.2 })
        }
      }, 1000)

      return () => clearTimeout(checkNodesVisibility)
    }
  }, [loading, currentMap, reactFlowInstance])

  const toggleNodeCollapse = useCallback((nodeId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    setCollapsedNodes((prev) => {
      const newCollapsed = new Set(prev)
      if (newCollapsed.has(nodeId)) {
        newCollapsed.delete(nodeId)
      } else {
        newCollapsed.add(nodeId)
      }
      return newCollapsed
    })
  }, [])

  const handleFullscreen = () => {
    if (reactFlowWrapperRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen()
      } else {
        reactFlowWrapperRef.current.requestFullscreen({ navigationUI: "auto" })
        reactFlowWrapperRef.current.style.backgroundColor = "#0c1321"
        const dotElements = reactFlowWrapperRef.current.querySelectorAll(".react-flow__background-dots circle")
        dotElements.forEach((dot: Element) => {
          if (dot instanceof SVGElement) {
            dot.style.fill = "#374151"
          }
        })
      }
    }
  }

  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && reactFlowWrapperRef.current) {
        reactFlowWrapperRef.current.style.backgroundColor = ""
        const dotElements = reactFlowWrapperRef.current.querySelectorAll(".react-flow__background-dots circle")
        dotElements.forEach((dot: Element) => {
          if (dot instanceof SVGElement) {
            dot.style.fill = ""
          }
        })
      }
    }

    document.addEventListener("fullscreenchange", handleFullscreenChange)
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange)
    }
  }, [])

  const handleFollow = async () => {
    if (!user?.id || !creatorProfile?.id) return;

    const isCurrentlyFollowing = isFollowing;
    const currentFollowersCount = creatorProfile.followers || 0;

    // Optimistically update UI
    setIsFollowing(!isCurrentlyFollowing);
    setCreatorProfile(prev => prev ? {
      ...prev,
      followers: isCurrentlyFollowing ? Math.max(currentFollowersCount - 1, 0) : currentFollowersCount + 1
    } : null);

    try {
      // Update followed_by array for the creator's profile
      const updatedFollowedBy = isCurrentlyFollowing
        ? (creatorProfile.followed_by || []).filter((id: string) => id !== user.id)
        : [...(creatorProfile.followed_by || []), user.id];

      // Calculate updated followers count
      const updatedFollowers = isCurrentlyFollowing
        ? Math.max(currentFollowersCount - 1, 0)
        : currentFollowersCount + 1;

      // Update the creator's profile in Supabase
      const { error: profileError } = await supabase
        .from("profiles")
        .update({
          followed_by: updatedFollowedBy,
          followers: updatedFollowers
        })
        .eq("id", creatorProfile.id);

      if (profileError) throw profileError;

      // Send notification for follow/unfollow
      if (user?.username && creatorProfile.id !== user.id) {
        await useNotificationStore.getState().addNotification({
          user_id: creatorProfile.id,
          type: 'follow',
          title: isCurrentlyFollowing ? 'Lost Follower' : 'New Follower',
          message: isCurrentlyFollowing
            ? `@${user.username} unfollowed you`
            : `@${user.username} started following you`,
          related_user: user.id
        });
      }

      // Fetch the authenticated user's current following list
      const { data: authUserProfile, error: authUserError } = await supabase
        .from("profiles")
        .select("following")
        .eq("id", user.id)
        .single();

      if (authUserError) throw authUserError;

      // Update following array for the current user
      const updatedFollowing = isCurrentlyFollowing
        ? (authUserProfile.following || []).filter((id: string) => id !== creatorProfile.id)
        : [...(authUserProfile.following || []), creatorProfile.id];

      // Update the current user's profile in Supabase
      const { error: followingError } = await supabase
        .from("profiles")
        .update({ following: updatedFollowing })
        .eq("id", user.id);

      if (followingError) throw followingError;

      // Emit event for Profile component to update following count
      eventEmitter.emit('followToggle', {
        action: isCurrentlyFollowing ? 'unfollow' : 'follow',
        targetUserId: creatorProfile.id
      });
    } catch (error) {
      console.error("Error updating follow status:", error);
      // Revert UI changes on failure
      setIsFollowing(isCurrentlyFollowing);
      setCreatorProfile(prev => prev ? {
        ...prev,
        followers: currentFollowersCount
      } : null);
    }
  };


  const handlePostComment = async () => {
    if (!user?.id || !newComment.trim() || !currentMap?.key) {
      return
    }

    const comment = {
      mindmap_id: currentMap.key,
      user_id: user.id,
      content: newComment.trim()
    }

    try {
      const { data, error } = await supabase
        .from("comments")
        .insert(comment)
        .select("id, content, created_at, user_id, profiles(username, avatar_url)")
        .single()

      if (error) {
        console.error("Error posting comment:", error)
      } else {
        setComments((prev) => [data, ...prev])
        setNewComment("")


        if (currentMap.creator !== user.id) {
          try {
            await useNotificationStore.getState().addNotification({
              user_id: currentMap.creator,
              type: 'comment',
              title: 'New Comment',
              message: `@${user.username} commented on your mindmap: ${currentMap.title}`,
              related_user: user.id,
              mindmap_key: currentMap.key,
              comment_id: data.id
            });
            console.log('Comment notification sent successfully');
          } catch (notificationError) {
            console.error('Failed to send comment notification:', notificationError);

          }
        }
      }
    } catch (error) {
      console.error("Error posting comment:", error)
    }
  }

  const handlePostReply = async (parentId: string, replyContent: string) => {
    if (!user?.id || !replyContent.trim() || !currentMap?.key) return

    const reply = {
      mindmap_id: currentMap.key,
      user_id: user.id,
      content: replyContent.trim(),
      parent_id: parentId
    }

    try {
      const { data, error } = await supabase
        .from("comments")
        .insert(reply)
        .select("id, content, created_at, user_id, parent_id, profiles(username, avatar_url)")
        .single()

      if (error) {
        console.error("Error posting reply:", error)
      } else {

        const parentComment = comments.find(c => c.id === parentId);

        setComments((prev) => {
          const parentIndex = prev.findIndex((c) => c.id === parentId)
          if (parentIndex !== -1) {
            const updatedParent = {
              ...prev[parentIndex],
              replies: [...(prev[parentIndex].replies || []), data],
            }
            return [...prev.slice(0, parentIndex), updatedParent, ...prev.slice(parentIndex + 1)]
          }
          return prev
        })


        if (currentMap.creator !== user.id) {
          try {
            await useNotificationStore.getState().addNotification({
              user_id: currentMap.creator,
              type: 'comment',
              title: 'New Reply',
              message: `@${user.username} replied to a comment on your mindmap: ${currentMap.title}`,
              related_user: user.id,
              mindmap_key: currentMap.key,
              comment_id: data.id
            });
            console.log('Reply notification sent to mindmap creator successfully');
          } catch (notificationError) {
            console.error('Failed to send reply notification to mindmap creator:', notificationError);

          }
        }


        if (parentComment && parentComment.user_id !== user.id && parentComment.user_id !== currentMap.creator) {
          try {
            await useNotificationStore.getState().addNotification({
              user_id: parentComment.user_id,
              type: 'comment',
              title: 'New Reply',
              message: `@${user.username} replied to your comment on mindmap: ${currentMap.title}`,
              related_user: user.id,
              mindmap_key: currentMap.key,
              comment_id: data.id
            });
            console.log('Reply notification sent to comment creator successfully');
          } catch (notificationError) {
            console.error('Failed to send reply notification to comment creator:', notificationError);

          }
        }
      }
    } catch (error) {
      console.error("Unexpected error posting reply:", error)
    }
  }


  const handleLikeComment = async (commentId: string, isReply = false, parentCommentId?: string) => {
    if (!user?.id) {
      return
    }

    const updateCommentLikes = (commentList: any[]): any[] =>
      commentList.map((comment) => {
        if (comment.id === commentId) {
          const isLiked = comment.liked_by?.includes(user.id)
          const updatedLikes = (comment.likes || 0) + (isLiked ? -1 : 1)
          const updatedLikedBy = isLiked
            ? (comment.liked_by || []).filter((id: string) => id !== user.id)
            : [...(comment.liked_by || []), user.id]

          return { ...comment, likes: updatedLikes, liked_by: updatedLikedBy }
        }

        if (comment.replies && comment.replies.length > 0) {
          return { ...comment, replies: updateCommentLikes(comment.replies) }
        }

        return comment
      })

    const updatedComments = isReply
      ? comments.map((comment) =>
          comment.id === parentCommentId
            ? { ...comment, replies: updateCommentLikes(comment.replies || []) }
            : comment
        )
      : updateCommentLikes(comments)

    setComments(updatedComments)

    try {
      const targetComment = isReply
        ? updatedComments
            .find((c: any) => c.id === parentCommentId)
            ?.replies.find((r: any) => r.id === commentId)
        : updatedComments.find((c: any) => c.id === commentId)

      if (targetComment) {
        const { error } = await supabase
          .from("comments")
          .update({
            likes: targetComment.likes,
            liked_by: targetComment.liked_by,
          })
          .eq("id", commentId)

        if (error) {
          console.error("Error updating comment likes in Supabase:", error)
        }
      }
    } catch (error) {
      console.error("Unexpected error updating comment likes:", error)
    }
  }

  const handleEditComment = async (commentId: string, updatedContent: string) => {
    if (!user?.id || !updatedContent.trim()) return

    try {
      const { data, error } = await supabase
        .from("comments")
        .update({ content: updatedContent.trim(), edited_at: new Date().toISOString() })
        .eq("id", commentId)
        .select("id, content, created_at, edited_at, user_id, profiles(username, avatar_url)")
        .single()

      if (error) {
        console.error("Error editing comment:", error)
      } else {
        setComments((prev) =>
          prev.map((c) => (c.id === commentId ? { ...c, content: updatedContent, edited_at: data.edited_at } : c)),
        )
      }
    } catch (error) {
      console.error("Unexpected error editing comment:", error)
    }
  }

  const handleConfirmDeleteComment = async () => {
    if (!commentToDelete || !user?.id) return;

    try {
      const { error } = await supabase.from("comments").delete().eq("id", commentToDelete);

      if (error) {
        console.error("Error deleting comment from Supabase:", error);
      } else {
        setComments((prev) => {
          const isTopLevel = prev.some(c => c.id === commentToDelete);

          if (isTopLevel) {
            return prev.filter(c => c.id !== commentToDelete);
          }
          return prev.map(comment => ({
            ...comment,
            replies: (comment.replies || []).filter((reply: any) => reply.id !== commentToDelete)
          }));
        });
        setCommentToDelete(null);
      }
    } catch (error) {
      console.error("Unexpected error deleting comment:", error);
    }
  };

  const getRelativeTime = (supabaseDate: string): string => {
    const now = new Date()
    const nowUTC = now.getTime() + now.getTimezoneOffset() * 60 * 1000
    const pastUTC = new Date(supabaseDate).getTime()

    const diffInSeconds = Math.floor((nowUTC - pastUTC) / 1000)

    if (diffInSeconds <= 0) return 'Just now'

    if (diffInSeconds < 60) return `${diffInSeconds} ${diffInSeconds === 1 ? 'second' : 'seconds'} ago`
    const diffInMinutes = Math.floor(diffInSeconds / 60)
    if (diffInMinutes < 60) return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 30) return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`
    const diffInMonths = Math.floor(diffInDays / 30)
    if (diffInMonths < 12) return `${diffInMonths} ${diffInMonths === 1 ? 'month' : 'months'} ago`
    const diffInYears = Math.floor(diffInMonths / 12)
    return `${diffInYears} ${diffInYears === 1 ? 'year' : 'years'} ago`
  }


  if (loading || !currentMap || !currentMap.nodes || !currentMap.edges || currentMap.nodes.length === 0) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-900">
        <div className="flex flex-col items-center">
          <Loader className="w-12 h-12 text-blue-500 animate-spin mb-4" />
          <p className="text-xl text-white">
            {loading ? "Loading mind map..." : "Preparing mind map..."}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4">      {isInfoModalOpen && (        <InfoModal
          mindmap={{
            username: username || '',
            displayName: creatorProfile?.avatar_url ? (username || '') : "Unknown User",
            name: currentMap.title,
            id: currentMap.id,
            updatedAt: currentMap.updated_at,
            description: currentMap.description || "No description provided.",
            visibility: currentMap.visibility,
            avatar_url: creatorProfile?.avatar_url,
            collaborators: currentMap.collaborators || [],
            published_at: currentMap.published_at,
            stats: {
              nodes: currentMap.nodes?.length || 0,
              edges: currentMap.edges?.length || 0,
              likes: currentMap.likes || 0,
              comments: totalCommentsCount || 0,
              saves: currentMap.saves || 0,
            }
          }}
          onClose={() => setIsInfoModalOpen(false)}
        />
      )}

      {isShareModalOpen && (
        <ShareModal
          title={currentMap.title}
          url={window.location.href}
          creator={username || ""}
          onClose={() => setIsShareModalOpen(false)}
          isMainMap={currentMap.is_main || false}
          mindmapId={currentMap.id}
        />
      )}

      <div className="h-[90vh] bg-gray-900 rounded-xl shadow-lg p-6 text-gray-100 w-[95vw] overflow-auto">
        <div className="flex flex-col items-start mb-4 relative">
          <div className="flex items-center gap-3">
            <a
              href={`/${username}`}
              className="w-10 h-10 rounded-full bg-gray-700 flex-shrink-0 overflow-hidden transition-transform hover:scale-105 hover:ring-2 hover:ring-blue-400"
            >
              {creatorProfile?.avatar_url ? (
                <img
                  src={creatorProfile.avatar_url || "/placeholder.svg"}
                  alt={username || "Creator"}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-300 font-medium">
                  {username?.charAt(0).toUpperCase() || "C"}
                </div>
              )}
            </a>
            <div>
              <h1 className="text-xl font-bold">{currentMap.title}</h1>
              <div className="flex items-center gap-2">
                <a href={`/${username}`} className="text-sm text-gray-400 mt-1 font-medium hover:underline">
                  @{username}
                </a>
                <span className="text-xs text-gray-500 mt-1">
                  {creatorProfile?.followers !== undefined && creatorProfile.followers > 0 ?
                    `${creatorProfile.followers} ${creatorProfile.followers === 1 ? 'follower' : 'followers'}` :
                    null}
                </span>
              </div>
            </div>
          </div>          <div className="absolute top-0 right-0 flex gap-2">
            {(isCreator || isCollaborator) && (
              <a
                href={`/${username}/${id}/edit`}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Edit
              </a>
            )}
            {!isCreator && user?.id && (
              <button
                onClick={handleFollow}
                className={`px-4 py-2 ${isFollowing ? "bg-red-500 hover:bg-red-600" : "bg-gray-800 hover:bg-gray-700"} text-white rounded-lg transition-colors flex items-center gap-1`}
              >
                {isFollowing ? (
                  <>
                    <UserMinus className="w-4 h-4" />
                    <span className="hidden sm:inline">Unfollow</span>
                  </>
                ) : (
                  <>
                    <UserPlus className="w-4 h-4" />
                    <span className="hidden sm:inline">Follow</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>

        <div className="flex flex-col md:flex-row h-auto md:h-[calc(100%-8rem)] gap-4">

          <div
            ref={reactFlowWrapperRef}
            className="h-[500px] md:h-full w-full md:w-[75%] border border-gray-700 rounded-lg overflow-hidden relative"
          >
            <ReactFlowProvider>
              <ReactFlow
                key={`reactflow-${currentMap.id}`}
                nodes={currentMap.nodes.map((node: any) => {
                  const hasChildren = currentMap.edges.some((edge: any) => edge.source === node.id)
                  const isHidden = (() => {
                    const findAncestors = (nodeId: string): string[] => {
                      const parentEdges = currentMap.edges.filter((edge: any) => edge.target === nodeId)
                      if (parentEdges.length === 0) return []
                      const parents = parentEdges.map((edge: any) => edge.source)
                      const grandparents = parents.flatMap(findAncestors)
                      return [...parents, ...grandparents]
                    }

                    const ancestors = findAncestors(node.id)
                    return ancestors.some((ancestorId) => collapsedNodes.has(ancestorId))
                  })()

                  const nodeLabel = hasChildren ? (
                    <div key={node.id} className="flex items-center justify-between w-full">
                      <div
                        className="break-words overflow-hidden"
                        style={{ wordBreak: "break-word", maxWidth: "calc(100% - 30px)" }}
                      >
                        {node.type === "default" && node.data.label === "" ? "Text..." : node.data.label}
                      </div>
                      <button
                        className="ml-2 p-1 rounded-full hover:bg-gray-700 transition-colors flex-shrink-0 z-10"
                        onClick={(e) => toggleNodeCollapse(node.id, e)}
                        title={collapsedNodes.has(node.id) ? "Expand" : "Collapse"}
                      >
                        <ChevronDown
                          className={`w-4 h-4 text-gray-300 transition-transform ${
                            collapsedNodes.has(node.id) ? "" : "transform rotate-180"
                          }`}
                        />
                      </button>
                    </div>
                  ) : node.type === "default" && node.data.label === "" ? (
                    "Text..."
                  ) : (
                    node.data.label
                  )

                  return {
                    ...node,
                    hidden: isHidden,
                    position: node.position || { x: 0, y: 0 },
                    style: {
                      ...node.style,
                      background: node.background || node.style?.background || (node.type && defaultNodeStyles[node.type as keyof typeof defaultNodeStyles]?.background),
                      width: ['link', 'mindmap', 'spotify', 'soundcloud', 'instagram', 'twitter', 'facebook', 'youtube', 'tiktok'].includes(node.type || '')
                        ? 'auto'
                        : node.style?.width || (node.type && defaultNodeStyles[node.type as keyof typeof defaultNodeStyles]?.width),
                      padding: node.type === 'image' ? '0' : (node.style?.padding || (node.type && defaultNodeStyles[node.type as keyof typeof defaultNodeStyles]?.padding))
                    },
                    data: {
                      ...node.data,
                      label: nodeLabel,
                    },
                  }
                })}
                edges={currentMap.edges}
                nodeTypes={nodeTypes}
                onInit={onInit}
                fitView
                nodesDraggable={true}
                nodesConnectable={true}
                elementsSelectable={true}
                zoomOnScroll={true}
                zoomOnDoubleClick={false}
                minZoom={0.1}
                maxZoom={2}
                proOptions={{ hideAttribution: true }}
              >
                <Background color="#374151" gap={20} />
                <Controls />
              </ReactFlow>
              <button
                onClick={handleFullscreen}
                className="absolute top-2 right-2 p-1 bg-gray-700 rounded-full hover:bg-gray-600 transition-colors"
                style={{ zIndex: 10 }}
              >
                <Maximize2 className="w-4 h-4 text-gray-300" />
              </button>
            </ReactFlowProvider>
          </div>


          <div className="hidden md:block w-full md:w-[25%] h-[400px] md:h-full border border-gray-700 rounded-lg overflow-auto">
            <div className="p-3 border-b border-gray-700 sticky top-0 bg-gray-900 z-10">
              <h3 className="font-medium text-gray-300">Similar Mindmaps</h3>
            </div>
            <div className="p-3">
              {similarMindmaps.map((mindmap, index) => (
                <div key={mindmap.id} className={`py-3 ${index > 0 ? "mt-4 border-t border-gray-800" : ""}`}>
                  <SimilarMindMapNode mindmap={mindmap} />
                </div>
              ))}
            </div>
          </div>
        </div>


        <div className="mt-6 flex items-center justify-between pb-4 border-b border-gray-700">
          <div className="flex items-center space-x-4">            <button
              onClick={(e) => hookHandleLike(e, currentMap)}
              className="flex items-center justify-center gap-2 px-4 py-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
            >
              <Heart
                className={`w-5 h-5 ${
                  user?.id && currentMap.likedBy?.includes(user.id) ? "fill-current text-sky-400" : "text-white"
                }`}
              />
              {currentMap.likes > 0 && <span>{currentMap.likes}</span>}
            </button>            <button
              onClick={(e) => hookHandleSave(e, currentMap)}
              className="flex items-center justify-center gap-2 px-4 py-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
            ><svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill={user?.id && currentMap.savedBy?.includes(user.id) ? "currentColor" : "none"}
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`lucide lucide-bookmark ${
                  user?.id && currentMap.savedBy?.includes(user.id) ? "text-sky-400" : "text-white"
                }`}
              >
                <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z" />
              </svg>
              {currentMap.saves > 0 && <span>{currentMap.saves}</span>}
            </button>
            <button
              onClick={() => setIsInfoModalOpen(true)}
              className="flex items-center justify-center gap-2 px-4 py-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-info"
              >
                <circle cx="12" cy="12" r="10" />
                <line x1="12" y1="16" x2="12" y2="12" />
                <line x1="12" y1="8" x2="12.01" y2="8" />
              </svg>
            </button>
            <button
              onClick={() => setIsShareModalOpen(true)
              }
              className="flex items-center justify-center gap-2 px-4 py-2 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
            >
              <Share2 className="w-5 h-5 text-white" />
            </button>
          </div>
          {currentMap.updated_at && (
            <p className="text-xs text-gray-500">
              Updated {new Date(currentMap.updated_at).toLocaleDateString()} at {new Date(currentMap.updated_at).toLocaleTimeString()}
            </p>
          )}
        </div>


        <div className="md:hidden mt-6 border border-gray-700 rounded-lg overflow-hidden">
          <div
            className="p-3 border-b border-gray-700 bg-gray-900 z-10 flex justify-between items-center cursor-pointer"
            onClick={() => setSimilarMindmapsCollapsed(!similarMindmapsCollapsed)}
          >
            <h3 className="font-medium text-gray-300">Similar Mindmaps</h3>
            <ChevronDown
              className={`w-5 h-5 text-gray-300 transition-transform ${similarMindmapsCollapsed ? "" : "transform rotate-180"}`}
            />
          </div>
          {!similarMindmapsCollapsed && (
            <div className="p-3 max-h-[400px] overflow-y-auto">
              {similarMindmaps.map((mindmap, index) => (
                <div key={mindmap.id} className={`py-3 ${index > 0 ? "mt-4 border-t border-gray-800" : ""}`}>
                  <SimilarMindMapNode mindmap={mindmap} />
                </div>
              ))}
            </div>
          )}
        </div>


        <div id="comments-section" className="mt-6">

          <h3 className="font-medium text-gray-300 mb-4 flex items-center">
            <span>Comments</span>
            <span className="ml-2 text-sm text-gray-400">({totalCommentsCount})</span>
          </h3>


          <div className="flex items-start gap-3 mb-6">
            <div className="w-10 h-10 rounded-full bg-gray-700 flex-shrink-0 overflow-hidden">
              {userProfile?.avatar_url ? (
                <img
                  src={userProfile.avatar_url || "/placeholder.svg"}
                  alt={user?.username || "User"}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-300 font-medium">
                  {user?.username?.charAt(0).toUpperCase() || "U"}
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="text-sm text-gray-400 mb-2">
                Comment as <span className="text-blue-400">@{user?.username || "username"}</span>
              </div>
              <textarea
                className="w-full bg-gray-800 border border-gray-700 rounded-lg p-3 text-sm min-h-[80px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Add a comment..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
              ></textarea>
              <div className="flex justify-end mt-2">
                <button
                  onClick={handlePostComment}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
                >
                  Comment
                </button>
              </div>
            </div>
          </div>


          <div className="space-y-6">
            {comments.map((comment) => (
              <div
                key={comment.id}
                id={`comment-${comment.id}`}
                className={`flex flex-col gap-3 p-3 rounded-lg transition-colors duration-500 ${highlightedCommentId === comment.id ? 'bg-blue-900/30 border border-blue-500' : ''}`}
              >
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 rounded-full bg-gray-700 flex-shrink-0 overflow-hidden">
                    {comment.profiles?.avatar_url ? (
                      <img
                        src={comment.profiles.avatar_url || "/placeholder.svg"}
                        alt={comment.profiles.username || "User"}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-300 font-medium">
                        {comment.profiles?.username?.charAt(0).toUpperCase() || "U"}
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium">@{comment.profiles?.username}</p>
                        <span className="text-xs text-gray-400">
                          {getRelativeTime(comment.created_at)}
                          {comment.edited_at && <span className="ml-1 text-gray-500">(edited)</span>}
                        </span>
                      </div>
                      <div className="relative">
                        <button
                          className="text-gray-400 hover:text-gray-300 p-2 rounded-full bg-gray-800 hover:bg-gray-700"
                          onClick={() =>
                            setComments((prev) =>
                              prev.map((c) => (c.id === comment.id ? { ...c, isMenuOpen: !c.isMenuOpen } : c))
                            )
                          }
                        >
                          <MoreHorizontal className="w-5 h-5" />
                        </button>
                        {comment.isMenuOpen && (
                          <div className="absolute right-0 mt-2 w-40 bg-gray-800 rounded-lg shadow-lg z-10">
                            {comment.user_id === user?.id && (
                              <button
                                className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-gray-300 hover:bg-gray-700"
                                onClick={() =>
                                  setComments((prev) =>
                                    prev.map((c) =>
                                      c.id === comment.id ? { ...c, isEditing: true, isMenuOpen: false } : c
                                    )
                                  )
                                }
                              >
                                <Edit2 className="w-4 h-4" />
                                Edit
                              </button>
                            )}
                            {(comment.user_id === user?.id || currentMap.creator === user?.id) && (
                              <button
                                className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-red-400 hover:bg-gray-700"
                                onClick={() => setCommentToDelete(comment.id)}
                              >
                                <Trash2 className="w-4 h-4" />
                                Delete
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    {comment.isEditing ? (
                      <div className="mt-2">
                        <textarea
                          className="w-full bg-gray-800 border border-gray-700 rounded-lg p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          defaultValue={comment.content}
                          onChange={(e) =>
                            setComments((prev) =>
                              prev.map((c) => (c.id === comment.id ? { ...c, draftContent: e.target.value } : c)),
                            )
                          }
                          autoFocus
                        />
                        <div className="flex items-center gap-2 mt-2">
                          <button
                            className="p-2 bg-green-500 text-white rounded-full hover:bg-green-600"
                            onClick={() => {
                              const updatedContent = comment.draftContent?.trim();
                              if (updatedContent && updatedContent !== comment.content) {
                                handleEditComment(comment.id, updatedContent);
                                setComments((prev) =>
                                  prev.map((c) =>
                                    c.id === comment.id
                                      ? { ...c, content: updatedContent, isEditing: false, isEdited: true }
                                      : c
                                  )
                                );
                              } else {
                                setComments((prev) =>
                                  prev.map((c) => (c.id === comment.id ? { ...c, isEditing: false } : c))
                                );
                              }
                            }}
                          >
                            <Check className="w-4 h-4" />
                          </button>
                          <button
                            className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600"
                            onClick={() =>
                              setComments((prev) =>
                                prev.map((c) => (c.id === comment.id ? { ...c, isEditing: false } : c))
                              )
                            }
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-300 mt-1">{comment.content}</p>
                    )}
                    <div className="flex items-center gap-4 mt-2">
                      <button
                        onClick={() => handleLikeComment(comment.id)}
                        className="flex items-center gap-2 text-xs text-gray-400 hover:text-gray-300"
                      >
                        <Heart
                          className={`w-4 h-4 ${
                            user?.id && comment.liked_by?.includes(user.id)
                              ? "fill-current text-sky-400"
                              : "text-gray-400"
                          }`}
                        />
                        {comment.likes > 0 && <span>{comment.likes}</span>}
                      </button>
                      <button
                        onClick={() =>
                          setComments((prev) =>
                            prev.map((c) => (c.id === comment.id ? { ...c, isReplying: !c.isReplying } : c))
                          )
                        }
                        className="text-xs text-gray-400 hover:text-gray-300"
                      >
                        Reply
                      </button>

                      {comment.replies && comment.replies.length > 0 && (
                        <button
                          onClick={() =>
                            setComments((prev) =>
                              prev.map((c) => (c.id === comment.id ? { ...c, showReplies: !c.showReplies } : c))
                            )
                          }
                          className="text-xs text-blue-400 hover:text-blue-300"
                        >
                          {comment.showReplies
                            ? "Hide replies"
                            : `${comment.replies.length} ${comment.replies.length === 1 ? "reply" : "replies"}`}
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {comment.isReplying && (
                  <div className="flex items-start gap-3 ml-10 mt-2">
                    <div className="w-8 h-8 rounded-full bg-gray-700 flex-shrink-0 overflow-hidden">
                      {userProfile?.avatar_url ? (
                        <img
                          src={userProfile.avatar_url || "/placeholder.svg"}
                          alt={user?.username || "User"}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-300 font-medium">
                          {user?.username?.charAt(0).toUpperCase() || "U"}
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <textarea
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Write a reply..."
                        value={comment.replyDraft || ""}
                        onChange={(e) =>
                          setComments((prev) =>
                            prev.map((c) => (c.id === comment.id ? { ...c, replyDraft: e.target.value } : c))
                          )
                        }
                      ></textarea>
                      <div className="flex justify-end mt-2 gap-2">
                        <button
                          onClick={() => {
                            if (comment.replyDraft?.trim()) {
                              handlePostReply(comment.id, comment.replyDraft);
                              setComments((prev) =>
                                prev.map((c) =>
                                  c.id === comment.id ? { ...c, isReplying: false, replyDraft: "", showReplies: true } : c
                                )
                              );
                            }
                          }}
                          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
                        >
                          Reply
                        </button>
                        <button
                          onClick={() =>
                            setComments((prev) =>
                              prev.map((c) => (c.id === comment.id ? { ...c, isReplying: false, replyDraft: "" } : c))
                            )
                          }
                          className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm font-medium"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {comment.replies && comment.replies.length > 0 && comment.showReplies && (
                  <div className="ml-10 mt-2 space-y-4 border-l-2 border-gray-700 pl-4">
                    {comment.replies.map((reply: any) => (
                      <div
                        key={reply.id}
                        id={`comment-${reply.id}`}
                        className={`flex items-start gap-3 p-2 rounded-lg transition-colors duration-500 ${highlightedCommentId === reply.id ? 'bg-blue-900/30 border border-blue-500' : ''}`}
                      >
                        <div className="w-8 h-8 rounded-full bg-gray-700 flex-shrink-0 overflow-hidden">
                          {reply.profiles?.avatar_url ? (
                            <img
                              src={reply.profiles.avatar_url || "/placeholder.svg"}
                              alt={reply.profiles.username || "User"}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-300 font-medium">
                              {reply.profiles?.username?.charAt(0).toUpperCase() || "U"}
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <p className="text-sm font-medium">@{reply.profiles?.username}</p>
                              <span className="text-xs text-gray-400">
                                {getRelativeTime(reply.created_at)}
                                {reply.edited_at && <span className="ml-1 text-gray-500">(edited)</span>}
                              </span>
                            </div>
                            <div className="relative">
                              <button
                                className="text-gray-400 hover:text-gray-300 p-1 rounded-full bg-gray-800 hover:bg-gray-700"
                                onClick={(e) => {
                                  e.stopPropagation();

                                  const updatedComments = [...comments];
                                  const parentIndex = updatedComments.findIndex((c) => c.id === comment.id);

                                  if (parentIndex !== -1) {

                                    const updatedReplies = updatedComments[parentIndex].replies.map((r: any) =>
                                      r.id === reply.id ? { ...r, isMenuOpen: !r.isMenuOpen } : { ...r, isMenuOpen: false }
                                    );

                                    updatedComments[parentIndex] = {
                                      ...updatedComments[parentIndex],
                                      replies: updatedReplies,
                                    };

                                    setComments(updatedComments);
                                  }
                                }}
                              >
                                <MoreHorizontal className="w-4 h-4" />
                              </button>
                              {reply.isMenuOpen && (
                                <div className="absolute right-0 mt-2 w-32 bg-gray-800 rounded-lg shadow-lg z-10">
                                  {reply.user_id === user?.id && (
                                    <button
                                      className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-gray-300 hover:bg-gray-700"
                                      onClick={(e) => {
                                        e.stopPropagation();

                                        const updatedComments = [...comments];
                                        const parentIndex = updatedComments.findIndex((c) => c.id === comment.id);

                                        if (parentIndex !== -1) {

                                          const updatedReplies = updatedComments[parentIndex].replies.map((r: any) =>
                                            r.id === reply.id ? { ...r, isEditing: true, isMenuOpen: false } : r
                                          );

                                          updatedComments[parentIndex] = {
                                            ...updatedComments[parentIndex],
                                            replies: updatedReplies,
                                          };

                                          setComments(updatedComments);
                                        }
                                      }}
                                    >
                                      <Edit2 className="w-4 h-4" />
                                      Edit
                                    </button>
                                  )}
                                  {(reply.user_id === user?.id || currentMap.creator === user?.id) && (
                                    <button
                                      className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-red-400 hover:bg-gray-700"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setCommentToDelete(reply.id);
                                      }}
                                    >
                                      <Trash2 className="w-4 h-4" />
                                      Delete
                                    </button>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                          {reply.isEditing ? (
                            <div className="mt-2">
                              <textarea
                                className="w-full bg-gray-800 border border-gray-700 rounded-lg p-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                defaultValue={reply.content}
                                onChange={(e) => {

                                  const updatedComments = [...comments];
                                  const parentIndex = updatedComments.findIndex((c) => c.id === comment.id);

                                  if (parentIndex !== -1) {

                                    const updatedReplies = updatedComments[parentIndex].replies.map((r: any) =>
                                      r.id === reply.id ? { ...r, draftContent: e.target.value } : r
                                    );

                                    updatedComments[parentIndex] = {
                                      ...updatedComments[parentIndex],
                                      replies: updatedReplies,
                                    };

                                    setComments(updatedComments);
                                  }
                                }}
                                autoFocus
                              />
                              <div className="flex items-center gap-2 mt-2">
                                <button
                                  className="p-2 bg-green-500 text-white rounded-full hover:bg-green-600"
                                  onClick={() => {
                                    const updatedContent = reply.draftContent?.trim();
                                    if (updatedContent && updatedContent !== reply.content) {
                                      handleEditComment(reply.id, updatedContent);


                                      const updatedComments = [...comments];
                                      const parentIndex = updatedComments.findIndex((c) => c.id === comment.id);

                                      if (parentIndex !== -1) {

                                        const updatedReplies = updatedComments[parentIndex].replies.map((r: any) =>
                                          r.id === reply.id
                                            ? { ...r, content: updatedContent, isEditing: false, edited_at: new Date().toISOString() }
                                            : r
                                        );

                                        updatedComments[parentIndex] = {
                                          ...updatedComments[parentIndex],
                                          replies: updatedReplies,
                                        };

                                        setComments(updatedComments);
                                      }
                                    } else {

                                      const updatedComments = [...comments];
                                      const parentIndex = updatedComments.findIndex((c) => c.id === comment.id);

                                      if (parentIndex !== -1) {
                                        const updatedReplies = updatedComments[parentIndex].replies.map((r: any) =>
                                          r.id === reply.id ? { ...r, isEditing: false } : r
                                        );

                                        updatedComments[parentIndex] = {
                                          ...updatedComments[parentIndex],
                                          replies: updatedReplies,
                                        };

                                        setComments(updatedComments);
                                      }
                                    }
                                  }}
                                >
                                  <Check className="w-4 h-4" />
                                </button>
                                <button
                                  className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600"
                                  onClick={() => {

                                    const updatedComments = [...comments];
                                    const parentIndex = updatedComments.findIndex((c) => c.id === comment.id);

                                    if (parentIndex !== -1) {
                                      const updatedReplies = updatedComments[parentIndex].replies.map((r: any) =>
                                        r.id === reply.id ? { ...r, isEditing: false } : r
                                      );

                                      updatedComments[parentIndex] = {
                                        ...updatedComments[parentIndex],
                                        replies: updatedReplies,
                                      };

                                      setComments(updatedComments);
                                    }
                                  }}
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          ) : (
                            <p className="text-sm text-gray-300 mt-1">{reply.content}</p>
                          )}
                          <div className="flex items-center gap-4 mt-2">

                            <button
                              onClick={() => handleLikeComment(reply.id, true, comment.id)}
                              className="flex items-center gap-2 text-xs text-gray-400 hover:text-gray-300"
                            >
                              <Heart
                                className={`w-4 h-4 ${
                                  user?.id && reply.liked_by?.includes(user.id)
                                    ? "fill-current text-sky-400"
                                    : "text-gray-400"
                                }`}
                              />
                              <span>{reply.likes || 0}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {commentToDelete && (
          <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-[90%] max-w-md text-center">
              <p className="text-gray-300 mb-4">Are you sure you want to delete this comment?</p>
              <div className="flex justify-center gap-4">
                <button
                  onClick={handleConfirmDeleteComment}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  Delete
                </button>
                <button
                  onClick={() => setCommentToDelete(null)}
                  className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ViewMindMap